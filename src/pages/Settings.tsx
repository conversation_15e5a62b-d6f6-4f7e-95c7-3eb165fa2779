import { useState } from 'react';

export const Settings = () => {
  const [customJson, setCustomJson] = useState('');
  const [notifications, setNotifications] = useState(true);

  const importPlatforms = async () => {
    console.log('Import platforms functionality');
  };



  return (
    <div className="p-8 space-y-8 max-w-4xl mx-auto">
      <div className="space-y-2">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-accentStart to-accentEnd bg-clip-text text-transparent">
          Settings
        </h2>
        <p className="text-textSecondary">Customize your Switch.AI experience</p>
      </div>

      {/* Appearance Section */}
      <section className="bg-bgSecondary rounded-2xl p-6 border border-border space-y-4">
        <h3 className="text-xl font-semibold text-textPrimary flex items-center space-x-2">
          <span>🎨</span>
          <span>Appearance</span>
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <label className="text-textPrimary font-medium">Notifications</label>
              <p className="text-sm text-textSecondary">Receive updates and alerts</p>
            </div>
            <button
              onClick={() => setNotifications(!notifications)}
              className={`relative w-12 h-6 rounded-full transition-colors duration-300 ${
                notifications ? 'bg-accentStart' : 'bg-border'
              }`}
            >
              <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform duration-300 ${
                notifications ? 'translate-x-6' : 'translate-x-0.5'
              }`} />
            </button>
          </div>
        </div>
      </section>

      {/* Keyboard Shortcuts Section */}
      <section className="bg-bgSecondary rounded-2xl p-6 border border-border space-y-4">
        <h3 className="text-xl font-semibold text-textPrimary flex items-center space-x-2">
          <span>⌨️</span>
          <span>Keyboard Shortcuts</span>
        </h3>

        <div className="grid grid-cols-2 gap-4">
          {[
            { keys: ['Ctrl', 'Shift', 'C'], action: 'Switch to Chat' },
            { keys: ['Ctrl', 'Shift', 'R'], action: 'Switch to Create' },
            { keys: ['Ctrl', 'Shift', 'L'], action: 'Switch to Lab' },
            { keys: ['Ctrl', 'Shift', 'H'], action: 'Switch to Hub' },
          ].map((shortcut, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-bgTertiary rounded-lg">
              <span className="text-textPrimary text-sm">{shortcut.action}</span>
              <div className="flex items-center space-x-1">
                {shortcut.keys.map((key, keyIndex) => (
                  <kbd key={keyIndex} className="px-2 py-1 bg-bgPrimary text-xs rounded border border-border font-mono">
                    {key}
                  </kbd>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Custom Platforms Section */}
      <section className="bg-bgSecondary rounded-2xl p-6 border border-border space-y-4">
        <h3 className="text-xl font-semibold text-textPrimary flex items-center space-x-2">
          <span>🔧</span>
          <span>Custom Platforms</span>
        </h3>

        <div className="space-y-4">
          <button
            onClick={importPlatforms}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd hover:from-accentHover hover:to-accentStart text-white rounded-xl transition-all duration-200 font-medium shadow-lg hover:shadow-xl hover:scale-105"
          >
            Import JSON Configuration
          </button>

          <textarea
            value={customJson}
            onChange={(e) => setCustomJson(e.target.value)}
            rows={6}
            className="w-full bg-bgTertiary border border-border rounded-xl p-4 text-sm font-mono resize-none focus:border-accentStart focus:ring-2 focus:ring-accentStart focus:ring-opacity-20 transition-all duration-200"
            placeholder={`{
  "platforms": [
    {
      "id": "custom-platform",
      "mode": "chat",
      "name": "Custom Platform",
      "url": "https://example.com",
      "iconFile": "custom.svg"
    }
  ]
}`}
          />
        </div>
      </section>

      {/* Danger Zone */}
      <section className="bg-red-900/20 rounded-2xl p-6 border border-red-800 space-y-4">
        <h3 className="text-xl font-semibold text-red-400 flex items-center space-x-2">
          <span>⚠️</span>
          <span>Danger Zone</span>
        </h3>

        <button className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-200 font-medium">
          Clear Cache & Reset Settings
        </button>
      </section>
    </div>
  );
};
