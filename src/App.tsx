import { TopBar } from './components/TopBar';
import { Sidebar } from './components/Sidebar';
import { WebView } from './components/WebView';
import { Settings } from './pages/Settings';
import { useAppStore } from './store';

export default function App() {
  const { activeMode } = useAppStore();

  return (
    <div className="h-screen flex flex-col">
      <TopBar />
      <div className="flex-1 flex">
        {activeMode === 'settings' ? (
          <Settings />
        ) : (
          <>
            <Sidebar />
            <WebView />
          </>
        )}
      </div>
    </div>
  );
}
