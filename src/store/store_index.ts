import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Platform } from '../types';

interface Store {
  activeMode: 'chat' | 'create' | 'lab' | 'hub' | 'settings';
  activePlatform: Platform | null;
  setMode: (mode: Store['activeMode']) => void;
  setPlatform: (platform: Platform) => void;
}

export const useAppStore = create<Store>()(
  persist(
    (set) => ({
      activeMode: 'chat',
      activePlatform: null,
      setMode: (m) => set({ activeMode: m, activePlatform: null }),
      setPlatform: (p) => set({ activePlatform: p }),
    }),
    { name: 'switch-ai-store' }
  )
);
