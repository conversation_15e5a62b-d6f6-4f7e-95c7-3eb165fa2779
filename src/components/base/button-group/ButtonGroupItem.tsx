import React from 'react';

type Props = {
  id: string;
  children: React.ReactNode;
  selected?: boolean;
  onSelect?: () => void;
};

export const ButtonGroupItem = ({ id, children, selected = false, onSelect }: Props) => {
  return (
    <button
      id={id}
      type="button"
      aria-pressed={selected}
      onClick={onSelect}
      className={`relative px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 ${
        selected
          ? 'text-white bg-gradient-to-r from-accentStart to-accentEnd shadow-lg'
          : 'text-textSecondary hover:text-textPrimary'
      }`}
    >
      <span>{children}</span>
    </button>
  );
};
