import React, { Children, cloneElement, isValidElement } from 'react';

type Props = {
  selectedKeys: Set<string>;
  onSelectionChange: (next: Set<string>) => void;
  children: React.ReactNode;
  className?: string;
};

export const ButtonGroup = ({ selectedKeys, onSelectionChange, children, className = '' }: Props) => {
  return (
    <div role="group" className={`inline-flex p-1 rounded-xl ${className}`}>
      {Children.map(children, (child) => {
        if (!isValidElement(child)) return child;
        const id = child.props.id;
        const selected = selectedKeys.has(id);
        return cloneElement(child, {
          ...child.props,
          selected,
          onSelect: () => {
            const next = new Set(selectedKeys);
            if (selected) {
              next.delete(id);
            } else {
              next.add(id);
            }
            onSelectionChange(next);
          },
        });
      })}
    </div>
  );
};
