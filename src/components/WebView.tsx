import { useEffect, useState } from 'react';
import { useAppStore } from '../store';
import { Webview } from '@tauri-apps/api/webview';
import { Window } from '@tauri-apps/api/window';
import { open } from '@tauri-apps/plugin-shell';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [webviewError, setWebviewError] = useState<string | null>(null);

  useEffect(() => {
    let embeddedWebview: Webview | null = null;

    const createEmbeddedWebview = async () => {
      if (!activePlatform) return;

      setIsLoading(true);
      setWebviewError(null);

      try {
        // Get the main window
        const mainWindow = Window.getCurrent();
        const webviewLabel = `embedded-${activePlatform.id}`;

        // Create embedded webview within the main window
        embeddedWebview = new Webview(mainWindow, webviewLabel, {
          url: activePlatform.url,
          x: 320, // Start after sidebar (approximately)
          y: 64,  // Start after topbar (approximately)
          width: 1080, // Adjust based on your layout
          height: 836, // Adjust based on your layout
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        });

        embeddedWebview.once('tauri://created', () => {
          setIsLoading(false);
        });

        embeddedWebview.once('tauri://error', (event) => {
          console.error(`Embedded webview error for ${activePlatform.name}:`, event);
          setWebviewError(`${activePlatform.name} failed to load.`);
          setIsLoading(false);
        });

      } catch (error) {
        console.error(`Failed to create embedded webview for ${activePlatform.name}:`, error);
        setWebviewError(`Could not embed ${activePlatform.name}.`);
        setIsLoading(false);
      }
    };

    createEmbeddedWebview();

    return () => {
      // Clean up embedded webviews
      Webview.getAll().then((webviews) => {
        webviews.forEach((w) => {
          if (w.label.startsWith('embedded-')) {
            try { w.close(); } catch {}
          }
        });
      });
    };
  }, [activePlatform]);

  const openInExternalBrowser = async () => {
    if (activePlatform) {
      try {
        await open(activePlatform.url);
      } catch (error) {
        console.error('Failed to open external browser:', error);
      }
    }
  };

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin. Your selected web app will load here.
          </p>
        </div>
      </div>
    </div>
  );

  if (!activePlatform) return <EmptyState />;

  if (webviewError) {
    return (
      <main className="flex-1 bg-bgPrimary relative flex flex-col items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-error to-warning rounded-2xl flex items-center justify-center">
            <span className="text-white text-xl">⚠️</span>
          </div>
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-textPrimary">Embedding Blocked or Failed</h3>
            <p className="text-textSecondary leading-relaxed">
              {webviewError}
            </p>
          </div>
          <button
            onClick={openInExternalBrowser}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-200 hover:scale-105"
          >
            Open {activePlatform.name} in Browser
          </button>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 bg-bgPrimary relative">
      {isLoading && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-bgPrimary z-10">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 border-4 border-accentStart border-t-transparent rounded-full animate-spin" />
            <p className="text-textSecondary">Loading {activePlatform.name}...</p>
          </div>
        </div>
      )}
      {/* The embedded webview will be positioned here by Tauri */}
    </main>
  );
};
