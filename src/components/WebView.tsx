import { useEffect, useState } from 'react';
import { useAppStore } from '../store';
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { open } from '@tauri-apps/plugin-shell';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [webviewError, setWebviewError] = useState<string | null>(null);

  useEffect(() => {
    let newWebview: WebviewWindow | null = null;

    const createWebview = async () => {
      if (!activePlatform) return;

      setIsLoading(true);
      setWebviewError(null);

      try {
        const webviewLabel = `platform-${activePlatform.id}`;

        newWebview = new WebviewWindow(webviewLabel, {
          url: activePlatform.url,
          title: activePlatform.name,
          width: 1200,
          height: 800,
          resizable: true,
          center: true,
          userAgent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          decorations: false,
          skipTaskbar: true,
          parent: 'main'
        });

        newWebview.once('tauri://created', () => {
          setIsLoading(false);
        });

        newWebview.once('tauri://error', (event) => {
          console.error(`WebView error for ${activePlatform.name}:`, event);
          setWebviewError(`${activePlatform.name} failed to load.`);
          setIsLoading(false);
        });

        // Optional: listen for navigation-blocked (some Tauri builds emit this)
        newWebview.listen && newWebview.listen('tauri://navigation-blocked', () => {
          setWebviewError(`${activePlatform.name} blocked embedding.`);
          setIsLoading(false);
        });

      } catch (error) {
        console.error(`Failed to create webview for ${activePlatform.name}:`, error);
        setWebviewError(`Could not create a window for ${activePlatform.name}.`);
        setIsLoading(false);
      }
    };

    createWebview();

    return () => {
      WebviewWindow.getAll().forEach((w) => {
        if (w.label !== 'main') {
          try { w.close(); } catch {}
        }
      });
    };
  }, [activePlatform]);

  const openInExternalBrowser = async () => {
    if (activePlatform) {
      try {
        await open(activePlatform.url);
      } catch (error) {
        console.error('Failed to open external browser:', error);
      }
    }
  };

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin. Your selected web app will load here.
          </p>
        </div>
      </div>
    </div>
  );

  if (!activePlatform) return <EmptyState />;

  if (webviewError) {
    return (
      <main className="flex-1 bg-bgPrimary relative flex flex-col items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-error to-warning rounded-2xl flex items-center justify-center">
            <span className="text-white text-xl">⚠️</span>
          </div>
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-textPrimary">Embedding Blocked or Failed</h3>
            <p className="text-textSecondary leading-relaxed">
              {webviewError}
            </p>
          </div>
          <button
            onClick={openInExternalBrowser}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-200 hover:scale-105"
          >
            Open {activePlatform.name} in Browser
          </button>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 bg-bgPrimary relative flex flex-col items-center justify-center">
      {isLoading ? (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-accentStart border-t-transparent rounded-full animate-spin" />
          <p className="text-textSecondary">Loading {activePlatform.name}...</p>
        </div>
      ) : (
        <div className="text-center space-y-4">
          <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
            <img src={`/icons/${activePlatform.iconFile}`} alt={activePlatform.name} className="w-12 h-12" />
          </div>
          <h3 className="text-2xl font-bold text-textPrimary">{activePlatform.name}</h3>
          <p className="text-textSecondary leading-relaxed">This platform has been opened in a separate window.</p>
        </div>
      )}
    </main>
  );
};
