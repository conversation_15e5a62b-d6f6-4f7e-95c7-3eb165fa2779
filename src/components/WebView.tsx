import { useEffect, useState } from 'react';
import { useAppStore } from '../store';
import { open } from '@tauri-apps/plugin-shell';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [iframeKey, setIframeKey] = useState(0);
  const [iframeError, setIframeError] = useState(false);

  useEffect(() => {
    if (activePlatform) {
      setIsLoading(true);
      setIframeError(false);
      setIframeKey((prev) => prev + 1);
      const timer = setTimeout(() => setIsLoading(false), 500);
      return () => clearTimeout(timer);
    }
  }, [activePlatform]);

  const handleIframeError = () => {
    setIframeError(true);
    setIsLoading(false);
  };

  const openInExternalBrowser = async () => {
    if (activePlatform) {
      try {
        await open(activePlatform.url);
      } catch (error) {
        console.error('Failed to open external browser:', error);
      }
    }
  };

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin. Your selected web app will load here.
          </p>
        </div>
      </div>
    </div>
  );

  if (!activePlatform) return <EmptyState />;

  // Error state when iframe is blocked
  if (iframeError) {
    return (
      <main className="flex-1 bg-bgPrimary relative flex flex-col items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-error to-warning rounded-2xl flex items-center justify-center">
            <span className="text-white text-xl">⚠️</span>
          </div>
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-textPrimary">Site Blocked Embedding</h3>
            <p className="text-textSecondary leading-relaxed">
              {activePlatform.name} prevents embedding for security. Click below to open in your default browser.
            </p>
          </div>
          <button
            onClick={openInExternalBrowser}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-200 hover:scale-105"
          >
            Open {activePlatform.name} in Browser
          </button>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 bg-bgPrimary relative flex flex-col">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-bgPrimary z-20">
          <div className="w-16 h-16 border-4 border-accentStart border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      <iframe
        key={iframeKey}
        src={activePlatform.url}
        title={activePlatform.name}
        className={`w-full h-full border-none transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation allow-downloads"
        onError={handleIframeError}
        onLoad={() => {
          // Check if iframe content loaded successfully
          const iframe = document.querySelector('iframe');
          if (iframe) {
            try {
              // Try to access iframe content - will throw if blocked
              iframe.contentWindow?.location.href;
            } catch (error) {
              // If we can't access it, it might be blocked
              setTimeout(() => {
                if (iframe.contentDocument?.body?.innerHTML === '') {
                  handleIframeError();
                }
              }, 2000);
            }
          }
        }}
      />
    </main>
  );
};
