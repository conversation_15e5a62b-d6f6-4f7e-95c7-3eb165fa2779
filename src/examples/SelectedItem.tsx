import { useState } from 'react';
import { ButtonGroup } from '../components/base/button-group/ButtonGroup';
import { ButtonGroupItem } from '../components/base/button-group/ButtonGroupItem';

export const SelectedItem = () => {
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set(['today']));

  return (
    <ButtonGroup selectedKeys={selectedKeys} onSelectionChange={setSelectedKeys}>
      <ButtonGroupItem id="today">Today</ButtonGroupItem>
      <ButtonGroupItem id="tomorrow">Tomorrow</ButtonGroupItem>
      <ButtonGroupItem id="thisweek">This week</ButtonGroupItem>
    </ButtonGroup>
  );
};
