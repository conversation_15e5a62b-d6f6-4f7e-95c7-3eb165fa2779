# Final Implementation Plan — ready for **Augment** AI Agent (VS Code)

> Execute the steps below in order. Replace/create files exactly as shown.

---

## 0. Prereqs (run in terminal)

```bash
# from repo root
# use your package manager (npm / pnpm / yarn)
pnpm install || npm install || yarn install

# ensure Tauri CLI + Rust toolchain available
# (agent should already have these; otherwise run:)
# npm install -g @tauri-apps/cli
# rustup toolchain install stable
```

---

## 1. Replace `src-tauri/tauri.conf.json`

Path: `src-tauri/tauri.conf.json` — replace entire file with:

```json
{
  "app": {
    "windows": [
      {
        "label": "main",
        "title": "Switch.AI",
        "width": 1400,
        "height": 900,
        "resizable": true,
        "center": true
      }
    ],
    "security": {
      "csp": null
    }
  },
  "plugins": {
    "shell": {
      "open": true
    },
    "webview": {
      "multiWebviews": true
    }
  },
  "permissions": [
    "core:default",
    "shell:default",
    "webview:default",
    "webview:create-webview-window"
  ]
}
```

---

## 2. Replace WebView component

Path: `src/components/WebView.tsx` — replace entire file with:

```tsx
import { useEffect, useState } from 'react';
import { useAppStore } from '../store';
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { open } from '@tauri-apps/plugin-shell';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [webviewError, setWebviewError] = useState<string | null>(null);

  useEffect(() => {
    let newWebview: WebviewWindow | null = null;

    const createWebview = async () => {
      if (!activePlatform) return;

      setIsLoading(true);
      setWebviewError(null);

      try {
        const webviewLabel = `platform-${activePlatform.id}`;

        newWebview = new WebviewWindow(webviewLabel, {
          url: activePlatform.url,
          title: activePlatform.name,
          width: 1200,
          height: 800,
          resizable: true,
          center: true,
          userAgent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          decorations: false,
          skipTaskbar: true,
          parent: 'main'
        });

        newWebview.once('tauri://created', () => {
          setIsLoading(false);
        });

        newWebview.once('tauri://error', (event) => {
          console.error(`WebView error for ${activePlatform.name}:`, event);
          setWebviewError(`${activePlatform.name} failed to load.`);
          setIsLoading(false);
        });

        // Optional: listen for navigation-blocked (some Tauri builds emit this)
        newWebview.listen && newWebview.listen('tauri://navigation-blocked', () => {
          setWebviewError(`${activePlatform.name} blocked embedding.`);
          setIsLoading(false);
        });

      } catch (error) {
        console.error(`Failed to create webview for ${activePlatform.name}:`, error);
        setWebviewError(`Could not create a window for ${activePlatform.name}.`);
        setIsLoading(false);
      }
    };

    createWebview();

    return () => {
      WebviewWindow.getAll().forEach((w) => {
        if (w.label !== 'main') {
          try { w.close(); } catch {}
        }
      });
    };
  }, [activePlatform]);

  const openInExternalBrowser = async () => {
    if (activePlatform) {
      try {
        await open(activePlatform.url);
      } catch (error) {
        console.error('Failed to open external browser:', error);
      }
    }
  };

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin. Your selected web app will load here.
          </p>
        </div>
      </div>
    </div>
  );

  if (!activePlatform) return <EmptyState />;

  if (webviewError) {
    return (
      <main className="flex-1 bg-bgPrimary relative flex flex-col items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-error to-warning rounded-2xl flex items-center justify-center">
            <span className="text-white text-xl">⚠️</span>
          </div>
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-textPrimary">Embedding Blocked or Failed</h3>
            <p className="text-textSecondary leading-relaxed">
              {webviewError}
            </p>
          </div>
          <button
            onClick={openInExternalBrowser}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-200 hover:scale-105"
          >
            Open {activePlatform.name} in Browser
          </button>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 bg-bgPrimary relative flex flex-col items-center justify-center">
      {isLoading ? (
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-accentStart border-t-transparent rounded-full animate-spin" />
          <p className="text-textSecondary">Loading {activePlatform.name}...</p>
        </div>
      ) : (
        <div className="text-center space-y-4">
          <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
            <img src={`/icons/${activePlatform.iconFile}`} alt={activePlatform.name} className="w-12 h-12" />
          </div>
          <h3 className="text-2xl font-bold text-textPrimary">{activePlatform.name}</h3>
          <p className="text-textSecondary leading-relaxed">This platform has been opened in a separate window.</p>
        </div>
      )}
    </main>
  );
};
```

---

## 3. Ensure JS deps present

Terminal:

```bash
pnpm add @tauri-apps/api @tauri-apps/plugin-shell || npm i @tauri-apps/api @tauri-apps/plugin-shell || yarn add @tauri-apps/api @tauri-apps/plugin-shell
```

---

## 4. Optional: Remove old iframe code

Path: `src/components/WebView.tsx` (old) — ensure no other component still renders an `<iframe>` for platforms. (If present, delete or replace.)

---

## 5. Run the app

Terminal:

```bash
# Option A - recommended via package script
pnpm tauri dev || npm run tauri dev || yarn tauri dev

# Option B - direct cargo
cd src-tauri
cargo tauri dev
```

---

## 6. (Optional) Add embedded-webview invoke route (advanced)

Create/replace `src-tauri/Cargo.toml` and `src-tauri/src/lib.rs` if you want **create\_embedded\_webview** via Rust.

`src-tauri/Cargo.toml`

```toml
[package]
name = "switch_ai"
version = "0.1.0"
edition = "2021"

[dependencies]
tauri = { version = "2", features = ["api-all"] }
tauri-plugin-shell = "2"
tauri-plugin-webview = "2"
```

`src-tauri/src/lib.rs`

```rust
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager};
use tauri_plugin_webview::{WebviewBuilder, WebviewUrl};

#[tauri::command]
async fn create_embedded_webview(
  app: tauri::AppHandle,
  url: String,
  label: String,
  parent_label: Option<String>,
  user_agent: Option<String>,
) -> Result<(), String> {
  let parent_name = parent_label.unwrap_or_else(|| "main".to_string());
  let parent = app
    .get_window(&parent_name)
    .ok_or_else(|| "parent window not found".to_string())?;

  let mut builder = WebviewBuilder::new(&label, WebviewUrl::External(
    url.parse().map_err(|e| e.to_string())?
  ));
  if let Some(ua) = user_agent {
    builder = builder.user_agent(ua);
  }

  builder
    .parent(&parent)
    .focus(true)
    .build()
    .map_err(|e| e.to_string())?;

  Ok(())
}

#[tauri::main]
async fn main() {
  tauri::Builder::default()
    .plugin(tauri_plugin_shell::init())
    .plugin(tauri_plugin_webview::init())
    .invoke_handler(tauri::generate_handler![create_embedded_webview])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
```

Then run:

```bash
cd src-tauri
cargo build
cd ..
pnpm tauri dev
```

---

## 7. Files changed checklist

```
✔ src-tauri/tauri.conf.json      (replaced)
✔ src/components/WebView.tsx     (replaced)
✔ package.json deps              (@tauri-apps/api, @tauri-apps/plugin-shell)
(optional) src-tauri/Cargo.toml
(optional) src-tauri/src/lib.rs
```

---

## 8. Quick test actions (in VS Code)

1. Open `src/components/WebView.tsx` — confirm new code saved.
2. Open `src-tauri/tauri.conf.json` — confirm content.
3. Run `pnpm tauri dev`.
4. Click a platform in the app sidebar — a separate webview window should open.
5. If failure occurs, click **Open in Browser** button.

---
