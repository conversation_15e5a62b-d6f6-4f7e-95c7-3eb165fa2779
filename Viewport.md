````markdown
# Fix: Keep Platforms Inside App Viewport (No Popup)

## Step 1 — Install plugin
```bash
pnpm add @tauri-apps/plugin-webview \
  || npm install @tauri-apps/plugin-webview \
  || yarn add @tauri-apps/plugin-webview
````

## Step 2 — Update Cargo.toml

`src-tauri/Cargo.toml`

```toml
[package]
name = "switch_ai"
version = "0.1.0"
edition = "2021"

[dependencies]
tauri = { version = "2", features = ["api-all"] }
tauri-plugin-shell = "2"
tauri-plugin-webview = "2"
```

## Step 3 — Add embedded command

`src-tauri/src/lib.rs`

```rust
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::Manager;
use tauri_plugin_webview::{WebviewBuilder, WebviewUrl};

#[tauri::command]
async fn open_embedded(
  app: tauri::AppHandle,
  url: String,
  label: String,
) -> Result<(), String> {
  let parent = app
    .get_window("main")
    .ok_or("main window not found")?;

  WebviewBuilder::new(&label, WebviewUrl::External(
    url.parse().map_err(|e| e.to_string())?
  ))
  .parent(&parent)
  .focus(true)
  .build()
  .map_err(|e| e.to_string())?;

  Ok(())
}

#[tauri::main]
async fn main() {
  tauri::Builder::default()
    .plugin(tauri_plugin_shell::init())
    .plugin(tauri_plugin_webview::init())
    .invoke_handler(tauri::generate_handler![open_embedded])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
```

## Step 4 — Update WebView\.tsx

`src/components/WebView.tsx`

```tsx
import { useEffect, useState } from 'react';
import { useAppStore } from '../store';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-shell';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const run = async () => {
      if (!activePlatform) return;
      setLoading(true);
      setError(null);
      try {
        await invoke('open_embedded', {
          url: activePlatform.url,
          label: `embed-${activePlatform.id}`
        });
        setLoading(false);
      } catch (err) {
        setLoading(false);
        setError(`${activePlatform.name} cannot be embedded`);
      }
    };
    run();
  }, [activePlatform]);

  if (!activePlatform) {
    return <div className="flex-1 flex items-center justify-center">Select a platform</div>;
  }

  if (error) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center space-y-4">
        <div>{error}</div>
        <button
          className="px-4 py-2 bg-accentStart text-white rounded-lg"
          onClick={() => open(activePlatform.url)}
        >
          Open in Browser
        </button>
      </div>
    );
  }

  if (loading) {
    return <div className="flex-1 flex items-center justify-center">Loading {activePlatform.name}…</div>;
  }

  return <div className="flex-1" />;
};
```

## Step 5 — Config

`src-tauri/tauri.conf.json`

```json
{
  "app": {
    "windows": [
      {
        "label": "main",
        "title": "Switch.AI",
        "width": 1400,
        "height": 900,
        "resizable": true,
        "center": true
      }
    ]
  },
  "plugins": {
    "shell": { "open": true },
    "webview": { "multiWebviews": true }
  },
  "permissions": [
    "core:default",
    "shell:default",
    "webview:default",
    "webview:create-webview"
  ]
}
```

## Step 6 — Run

```bash
pnpm tauri dev \
  || npm run tauri dev \
  || yarn tauri dev
```

---

This keeps every platform inside the **main app window viewport** instead of spawning popup frames.
