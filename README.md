# 🚀 Switch.AI - AI Platform Browser

A simple desktop app to quickly access 40+ AI platforms in one place.

## 🎯 Features

- **4 Modes**: <PERSON><PERSON>, Create, Lab, Hub
- **48 AI Platforms**: ChatGPT, Claude, Midjourney, and more
- **Quick Switching**: Click icons to open platforms in new windows
- **Dark Theme**: Purple gradient design
- **Desktop Only**: macOS (Intel/Silicon), Windows, and Linux

## 🏗️ For Developers

### Quick Start
```bash
npm install
npm run tauri dev
```

### Build for Friends
```bash
npm run tauri build
```

**Output**: `src-tauri/target/release/bundle/macos/Switch.AI.app`

### Share Instructions
1. **macOS**: Right-click the `.app` → Open (first time only)
2. **Windows**: Run the `.msi` installer

## 📁 Project Structure

```
src/
├── components/     # UI components
├── data/          # 48 platforms array
├── store/         # Zustand state
├── styles/        # Tailwind CSS
└── types/         # TypeScript interfaces
```

## 🎨 Adding Platforms

Edit `src/data/platforms.ts`:
```ts
{
  id: 'newai',
  mode: 'chat',
  name: 'New AI',
  url: 'https://newai.com',
  iconFile: 'newai.svg'
}
```

Add the icon to `public/icons/newai.svg`.

## 🔧 Tech Stack

- **Tauri** - Desktop framework
- **React + TypeScript** - Frontend
- **Tailwind CSS** - Styling
- **Zustand** - State management

## 🐧 Building for Linux (Debian/Ubuntu)

### 1. Install System Dependencies
First, you need to install the libraries required by Tauri for webview rendering and compiling.

```bash
sudo apt-get update
sudo apt-get install libwebkit2gtk-4.0-dev \
    build-essential \
    curl \
    wget \
    file \
    libssl-dev \
    libgtk-3-dev \
    libayatana-appindicator3-dev \
    librsvg2-dev
```

### 2. Install Rust
If you don't have Rust installed, you can install it via `rustup`:

```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```
Follow the on-screen instructions and make sure to restart your terminal after installation.

### 3. Install Node.js and pnpm/npm/yarn
Ensure you have a recent version of Node.js (v18+ recommended) and a package manager like npm.

### 4. Build the App
Once all dependencies are installed, you can build the application:

```bash
npm run tauri build
```

The compiled application (an `.AppImage` and a `.deb` file) will be located in `src-tauri/target/release/bundle/`.

---

**Note**: This is a hobby project for friends. No signing, no app store, no CI/CD. Just build and share! 🎉
