{"$schema": "https://schema.tauri.app/config/2", "productName": "Switch.AI", "version": "1.0.0", "identifier": "com.switchai.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Switch AI Browser", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false}], "security": {"csp": "default-src 'self' https:; connect-src https: wss:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https: data:; frame-src https:; child-src https:; object-src 'none'; base-uri 'self';"}}, "bundle": {"active": true, "targets": ["dmg", "msi", "appimage"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}