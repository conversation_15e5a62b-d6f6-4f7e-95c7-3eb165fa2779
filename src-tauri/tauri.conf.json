{"$schema": "https://schema.tauri.app/config/2", "productName": "Switch.AI", "version": "1.0.0", "identifier": "com.switchai.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "Switch.AI", "width": 1400, "height": 900, "resizable": true, "center": true}], "security": {"csp": null}}, "plugins": {"shell": {"open": true}}, "bundle": {"active": true, "targets": ["dmg", "msi", "appimage"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}